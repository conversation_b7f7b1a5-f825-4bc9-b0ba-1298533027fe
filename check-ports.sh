#!/bin/bash

# Port conflict checker for Echo Bot services
# Checks if ports 8201-8204 are available before starting services

echo "🔍 Checking port availability for Echo Bot services..."
echo "Required ports: 8201 (API docs), 8202 (Traefik dashboard), 8203 (Grafana), 8204 (Loki)"
echo ""

REQUIRED_PORTS="8201 8202 8203 8204"
PORT_CONFLICTS=""
CONFLICT_DETAILS=""

for port in $REQUIRED_PORTS; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        PORT_CONFLICTS="$PORT_CONFLICTS $port"
        
        # Try to get process details
        PROCESS_INFO=$(lsof -i :$port 2>/dev/null | tail -n +2 | head -1)
        if [ -n "$PROCESS_INFO" ]; then
            CONFLICT_DETAILS="$CONFLICT_DETAILS\n  Port $port: $PROCESS_INFO"
        else
            CONFLICT_DETAILS="$CONFLICT_DETAILS\n  Port $port: In use (process details unavailable)"
        fi
    fi
done

if [ -n "$PORT_CONFLICTS" ]; then
    echo "❌ Port conflicts detected on ports:$PORT_CONFLICTS"
    echo ""
    echo "📋 Conflict details:"
    echo -e "$CONFLICT_DETAILS"
    echo ""
    echo "💡 Solutions:"
    echo "  1. Stop the conflicting services"
    echo "  2. Kill specific processes: sudo kill -9 <PID>"
    echo "  3. Stop Docker containers: docker-compose down"
    echo "  4. Modify port mappings in docker-compose.yml"
    echo ""
    exit 1
else
    echo "✅ All required ports (8201-8204) are available!"
    echo "🚀 You can safely start the services with: docker-compose up -d"
    echo ""
    exit 0
fi
