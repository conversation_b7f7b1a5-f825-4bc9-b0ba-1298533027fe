---
# Traefik Configuration with Multiple Instances and Sticky Sessions
# Supports WebSocket connections and document processing with session affinity

services:
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API docs port
      - "8202:8202"   # Traefik dashboard port
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro
      # Traefik configuration
      - ./config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/
      # Use existing Let's Encrypt certificates - mount entire letsencrypt directory to resolve symlinks
      - /etc/letsencrypt/:/etc/letsencrypt/:ro
    environment:
      - DOMAIN=${DOMAIN:-localhost}
    command:
      - /bin/sh
      - -c
      - |
        echo "🔍 Checking for port conflicts..."

        # Check if required ports are available
        REQUIRED_PORTS="8201 8202"
        PORT_CONFLICTS=""

        for port in $$REQUIRED_PORTS; do
          if netstat -tuln 2>/dev/null | grep -q ":$$port "; then
            PORT_CONFLICTS="$$PORT_CONFLICTS $$port"
          fi
        done

        if [ -n "$$PORT_CONFLICTS" ]; then
          echo "❌ Port conflicts detected on ports:$$PORT_CONFLICTS"
          echo "💡 Please stop services using these ports or modify docker-compose.yml"
          echo "🔍 To check what's using these ports, run: lsof -i :PORT_NUMBER"
          exit 1
        else
          echo "✅ All required ports (8201-8202) are available"
        fi

        echo "🌐 DOMAIN: $$DOMAIN"
        echo "📜 Using existing Let's Encrypt certificates from /etc/letsencrypt/live/$$DOMAIN/"
        echo "🔍 Checking certificate files..."

        CERT_DIR="/etc/letsencrypt/live/$$DOMAIN"
        if [ -d "$$CERT_DIR" ]; then
          echo "✅ Certificate directory found: $$CERT_DIR"
          ls -la "$$CERT_DIR"

          if [ -f "$$CERT_DIR/fullchain.pem" ]; then
            echo "✅ fullchain.pem found"
            openssl x509 -in "$$CERT_DIR/fullchain.pem" -text -noout | grep -E "(Subject:|Issuer:|Not After)" || echo "❌ Cannot read certificate"
          else
            echo "❌ fullchain.pem not found"
          fi

          if [ -f "$$CERT_DIR/privkey.pem" ]; then
            echo "✅ privkey.pem found"
          else
            echo "❌ privkey.pem not found"
          fi

          # Generate dynamic TLS configuration
          echo "🔧 Generating dynamic TLS configuration..."
          cat > /etc/traefik/conf/dynamic-tls.yaml << EOF
        # Dynamic TLS Configuration for $$DOMAIN
        tls:
          certificates:
            - certFile: $$CERT_DIR/fullchain.pem
              keyFile: $$CERT_DIR/privkey.pem
        EOF
          echo "✅ Dynamic TLS configuration created"
        else
          echo "❌ Certificate directory not found: $$CERT_DIR"
        fi

        echo "🚀 Starting Traefik..."
        traefik --configfile=/etc/traefik/traefik.yaml
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"

      # HTTP to HTTPS redirect - only if domain is provided
      - "${DOMAIN:+traefik.http.routers.http-redirect.rule=Host(`${DOMAIN}`)}"
      - "${DOMAIN:+traefik.http.routers.http-redirect.entrypoints=web}"
      - "${DOMAIN:+traefik.http.routers.http-redirect.middlewares=https-redirect@file}"

      # Traefik dashboard on port 8202 - works with or without domain
      - "traefik.http.routers.traefik-dashboard.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=traefik-dashboard"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      - "traefik.http.routers.traefik-dashboard.middlewares=api-cors@file"

  # Dedicated Document Processing Instances (1-2 instances for WebSocket + process-documents)
  # These instances handle ONLY document processing flow with guaranteed sticky sessions
  eko-api-docs:
    build: .
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-docs
      - INSTANCE_ID=${HOSTNAME:-eko-api-docs}
      - INSTANCE_TYPE=document-processing
      - TZ=Asia/Kathmandu
    volumes:
      - /etc/hosts:/etc/hosts:ro
    networks:
      - eko-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    labels:
      - "traefik.enable=true"

      # CRITICAL: Document processing endpoints - DEDICATED instances only
      # Process documents endpoint - highest priority, dedicated service
      - "traefik.http.routers.process-docs.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/process-documents`)"
      - "traefik.http.routers.process-docs.entrypoints=websecure,http-api"
      - "traefik.http.routers.process-docs.service=eko-docs-service"
      - "traefik.http.routers.process-docs.middlewares=api-cors@file,document-headers@file"
      - "traefik.http.routers.process-docs.priority=100"
      - "${DOMAIN:+traefik.http.routers.process-docs.tls=true}"

      # WebSocket setup_files endpoint - SAME dedicated instances as process-documents
      - "traefik.http.routers.websocket.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/setup_files`)"
      - "traefik.http.routers.websocket.entrypoints=websecure,http-api"
      - "traefik.http.routers.websocket.service=eko-docs-service"
      - "traefik.http.routers.websocket.middlewares=websocket-headers@file,api-cors@file"
      - "traefik.http.routers.websocket.priority=99"
      - "${DOMAIN:+traefik.http.routers.websocket.tls=true}"

      # Check status endpoint - SAME dedicated instances
      - "traefik.http.routers.check-status.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/check_status`)"
      - "traefik.http.routers.check-status.entrypoints=websecure,http-api"
      - "traefik.http.routers.check-status.service=eko-docs-service"
      - "traefik.http.routers.check-status.middlewares=api-cors@file"
      - "traefik.http.routers.check-status.priority=98"
      - "${DOMAIN:+traefik.http.routers.check-status.tls=true}"

      # Dedicated service configuration with sticky sessions for document processing
      - "traefik.http.services.eko-docs-service.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.timeout=10s"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.scheme=http"

      # Session stickiness with cookies - CRITICAL for document processing flow
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.name=eko-docs-session"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.secure=false"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.httpOnly=false"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.sameSite=none"

      # Load balancer strategy - ensures consistent routing
      - "traefik.http.services.eko-docs-service.loadbalancer.passhostheader=true"

  # General API Instances (4+ instances for all other traffic)
  # Use: docker-compose up --scale eko-api=4 --scale eko-api-docs=1 -d
  eko-api:
    build: .
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
      - INSTANCE_ID=${HOSTNAME:-eko-api}
      - INSTANCE_TYPE=general
      - TZ=Asia/Kathmandu
    volumes:
      - /etc/hosts:/etc/hosts:ro
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - eko-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    labels:
      - "traefik.enable=true"

      # Main HTTPS API routes - only if domain is provided
      - "${DOMAIN:+traefik.http.routers.api-secure.rule=Host(`${DOMAIN}`)}"
      - "${DOMAIN:+traefik.http.routers.api-secure.entrypoints=websecure}"
      - "${DOMAIN:+traefik.http.routers.api-secure.tls=true}"
      - "${DOMAIN:+traefik.http.routers.api-secure.service=eko-api-service}"
      - "${DOMAIN:+traefik.http.routers.api-secure.middlewares=default-headers@file,api-cors@file}"

      # General API routes - handles all traffic EXCEPT document processing
      - "traefik.http.routers.api-docs.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.api-docs.entrypoints=http-api"
      - "traefik.http.routers.api-docs.service=eko-api-service"
      - "traefik.http.routers.api-docs.middlewares=api-cors@file,compression@file"
      - "traefik.http.routers.api-docs.priority=1"

      # General service configuration - handles all non-document processing traffic
      - "traefik.http.services.eko-api-service.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.timeout=10s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.scheme=http"

      # Load balancer strategy for general traffic
      - "traefik.http.services.eko-api-service.loadbalancer.passhostheader=true"

  # Lightweight Log Viewer - Simple web interface for logs
  log-viewer:
    image: amir20/dozzle:latest
    container_name: eko-log-viewer
    restart: unless-stopped
    ports:
      - "8203:8080"  # Log viewer dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - DOZZLE_LEVEL=info
      - DOZZLE_TAILSIZE=300
    networks:
      - eko-network
    mem_limit: 30m
    cpus: 0.1

networks:
  eko-network:
    driver: bridge


