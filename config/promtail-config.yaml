server:
  http_listen_port: 9080
  grpc_listen_port: 0
  log_level: warn

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Real-time Docker logs to Loki
  - job_name: docker
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          __path__: /var/lib/docker/containers/*/*-json.log

    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs: attrs
            time: time

      - json:
          expressions:
            tag: tag
          source: attrs

      - regex:
          expression: (?P<container_name>(?:[^|]*))
          source: tag

      - labels:
          stream:
          container_name:

      - timestamp:
          source: time
          format: RFC3339Nano
