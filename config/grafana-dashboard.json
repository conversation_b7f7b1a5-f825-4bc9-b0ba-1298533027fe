{"dashboard": {"id": null, "title": "Echo Bot Services Logs", "tags": ["echo-bot", "logs"], "timezone": "browser", "panels": [{"id": 1, "title": "All Services Logs", "type": "logs", "targets": [{"expr": "{job=\"docker\"}", "refId": "A"}], "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "options": {"showTime": true, "showLabels": true, "sortOrder": "Descending"}}, {"id": 2, "title": "API Services Only", "type": "logs", "targets": [{"expr": "{container_name=~\"echo_bot-eko-api.*\"}", "refId": "A"}], "gridPos": {"h": 12, "w": 12, "x": 0, "y": 12}}, {"id": 3, "title": "<PERSON><PERSON><PERSON>", "type": "logs", "targets": [{"expr": "{job=\"docker\"} |= \"ERROR\"", "refId": "A"}], "gridPos": {"h": 12, "w": 12, "x": 12, "y": 12}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}